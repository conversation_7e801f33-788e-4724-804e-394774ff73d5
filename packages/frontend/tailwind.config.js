/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'floppy-pink': '#ff69b4',
        'floppy-purple': '#8b5cf6',
        'floppy-green': '#10b981',
        'floppy-blue': '#4f46e5',
        'floppy-yellow': '#fbbf24',
        'floppy-orange': '#f97316',
      },
      fontFamily: {
        'display': ['Inter', 'system-ui', 'sans-serif'],
      },
      borderRadius: {
        'xl': '1rem',
        '2xl': '1.5rem',
        '3xl': '2rem',
      },
      animation: {
        'bounce-slow': 'bounce 2s infinite',
        'pulse-slow': 'pulse 3s infinite',
      }
    },
  },
  plugins: [require('daisyui')],
  daisyui: {
    themes: [
      {
        fasttransfer: {
          "primary": "#4f46e5", // Bright blue for primary actions
          "secondary": "#ec4899", // Pink for secondary elements
          "accent": "#06b6d4", // Cyan accent
          "neutral": "#1f2937", // Dark neutral
          "base-100": "#ffffff", // White background
          "base-200": "#f8fafc", // Very light gray
          "base-300": "#e2e8f0", // Light gray
          "info": "#3b82f6", // Blue for info
          "success": "#10b981", // Bright green for success
          "warning": "#f59e0b", // Orange for warnings
          "error": "#ef4444", // Red for errors
        },
      },
      "light",
      "dark",
    ],
    base: true,
    styled: true,
    utils: true,
  },
}
