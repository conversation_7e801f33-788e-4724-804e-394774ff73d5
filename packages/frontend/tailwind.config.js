/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'floppy-pink': '#ff69b4',
        'floppy-purple': '#8b5cf6',
        'floppy-green': '#10b981',
        'floppy-blue': '#1FE183',
        'floppy-yellow': '#fbbf24',
        'floppy-orange': '#f97316',
      },
      fontFamily: {
        'display': ['Inter', 'system-ui', 'sans-serif'],
      },
      borderRadius: {
        'xl': '1rem',
        '2xl': '1.5rem',
        '3xl': '2rem',
      },
      animation: {
        'bounce-slow': 'bounce 2s infinite',
        'pulse-slow': 'pulse 3s infinite',
      }
    },
  },
  plugins: [require('daisyui')],
  daisyui: {
    themes: [
      {
        fasttransfer: {
          "primary": "#1FE183", // Bright green for primary actions (matching the image)
          "secondary": "#ec4899", // Pink for secondary elements
          "accent": "#06b6d4", // Cyan accent
          "neutral": "#1f2937", // Dark neutral
          "base-100": "#000000", // Very dark background (almost black)
          "base-200": "#000000", // Dark gray background
          "base-300": "#000000", // Lighter dark gray
          "base-content": "#ffffff", // White text
          "info": "#1FE183", // Blue for info
          "success": "#22c55e", // Bright green for success
          "warning": "#f59e0b", // Orange for warnings
          "error": "#ef4444", // Red for errors
        },
      },
      "light",
      "dark",
    ],
    base: true,
    styled: true,
    utils: true,
  },
}
