import { useState, useEffect } from 'react';
import {
  Download,
  Search,
  Grid,
  List,
  Trash2,
  FileText,
  HardDrive,
  CheckCircle,
  Clock,
  AlertCircle,
  MoreVertical,
  ArrowUpDown,
  RefreshCw,
  Info,
  X,
  Archive,
  File
} from 'lucide-react';
import {
  getTransferHistory,
  downloadCompressedFile,
  downloadOriginalFile,
  deleteTransfer,
  bulkDeleteTransfers,
  // updateTransfer, // TODO: Remove if not needed
  getTransferStats
} from '../services/api';
import type {
  TransferHistoryItem,
  TransferHistoryParams,
  TransferStats,
  // UpdateTransferRequest // TODO: Remove if not needed
} from '../services/api';
import { LightningIcon } from './LightningIcon';

type Transfer = TransferHistoryItem;

type ViewMode = 'grid' | 'list';
type SortField = 'filename' | 'size' | 'createdAt' | 'status';
type SortOrder = 'asc' | 'desc';

export default function Downloads() {
  const [transfers, setTransfers] = useState<Transfer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [sortField, setSortField] = useState<SortField>('createdAt');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [selectedTransfers, setSelectedTransfers] = useState<Set<string>>(new Set());
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFileDetails, setSelectedFileDetails] = useState<Transfer | null>(null);
  const [stats, setStats] = useState<TransferStats | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    loadTransfers();
    loadStats();
  }, []);

  useEffect(() => {
    loadTransfers();
  }, [statusFilter, searchQuery, sortField, sortOrder, currentPage]);

  const loadTransfers = async () => {
    try {
      setLoading(true);
      setError('');

      const params: TransferHistoryParams = {
        status: statusFilter === 'all' ? undefined : statusFilter,
        search: searchQuery || undefined,
        sortBy: sortField,
        sortOrder,
        page: currentPage,
        limit: 20
      };

      const response = await getTransferHistory(params);
      setTransfers(response.transfers);
      setTotalPages(response.pagination.totalPages);
    } catch (error: any) {
      console.error('Error loading transfers:', error);
      setError('Failed to load transfers');
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const statsData = await getTransferStats();
      setStats(statsData);
    } catch (err) {
      console.error('Error loading stats:', err);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadTransfers();
    setRefreshing(false);
  };

  const handleDownload = async (transfer: Transfer, downloadType: 'compressed' | 'original' = 'compressed') => {
    try {
      if (downloadType === 'original') {
        await downloadOriginalFile(transfer.transferId, transfer.filename);
      } else {
        await downloadCompressedFile(transfer.transferId, transfer.filename);
      }
    } catch (error) {
      console.error('Download failed:', error);
      alert(`Failed to download ${downloadType} file`);
    }
  };

  const handleDelete = async (transferId: string) => {
    if (!confirm('Are you sure you want to delete this transfer?')) return;

    try {
      await deleteTransfer(transferId);
      await loadTransfers(); // Reload to get updated data
      setSelectedTransfers(prev => {
        const newSet = new Set(prev);
        newSet.delete(transferId);
        return newSet;
      });
      await loadStats(); // Update stats
    } catch (error) {
      console.error('Delete failed:', error);
      alert('Failed to delete transfer');
    }
  };

  const handleBulkDelete = async () => {
    if (selectedTransfers.size === 0) return;

    const count = selectedTransfers.size;
    if (!confirm(`Are you sure you want to delete ${count} transfer${count > 1 ? 's' : ''}?`)) return;

    try {
      setIsDeleting(true);
      const transferIds = Array.from(selectedTransfers);
      const result = await bulkDeleteTransfers(transferIds);

      if (result.failed.length > 0) {
        alert(`${result.deleted.length} transfers deleted successfully. ${result.failed.length} failed to delete.`);
      }

      await loadTransfers(); // Reload to get updated data
      setSelectedTransfers(new Set());
      await loadStats(); // Update stats
    } catch (error) {
      console.error('Bulk delete failed:', error);
      alert('Failed to delete transfers');
    } finally {
      setIsDeleting(false);
    }
  };

  const toggleSelectTransfer = (transferId: string) => {
    setSelectedTransfers(prev => {
      const newSet = new Set(prev);
      if (newSet.has(transferId)) {
        newSet.delete(transferId);
      } else {
        newSet.add(transferId);
      }
      return newSet;
    });
  };

  const toggleSelectAll = () => {
    if (selectedTransfers.size === transfers.length) {
      setSelectedTransfers(new Set());
    } else {
      setSelectedTransfers(new Set(transfers.map(t => t.transferId)));
    }
  };

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('asc');
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ready': return <CheckCircle className="w-4 h-4 text-success" />;
      case 'compressing': return <Clock className="w-4 h-4 text-warning" />;
      case 'uploading': return <Clock className="w-4 h-4 text-info" />;
      case 'error': return <AlertCircle className="w-4 h-4 text-error" />;
      default: return <Clock className="w-4 h-4 text-base-content/50" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "badge badge-sm font-medium";
    switch (status) {
      case 'ready': return `${baseClasses} badge-success`;
      case 'compressing': return `${baseClasses} badge-warning`;
      case 'uploading': return `${baseClasses} badge-info`;
      case 'error': return `${baseClasses} badge-error`;
      default: return `${baseClasses} badge-ghost`;
    }
  };

  const getFileIcon = (filename: string) => {
    const extension = filename.split('.').pop()?.toLowerCase();
    const iconClasses = "w-6 h-6";

    switch (extension) {
      case 'pdf':
        return <FileText className={`${iconClasses} text-red-500`} />;
      case 'doc':
      case 'docx':
        return <FileText className={`${iconClasses} text-blue-500`} />;
      case 'xls':
      case 'xlsx':
        return <FileText className={`${iconClasses} text-green-500`} />;
      case 'ppt':
      case 'pptx':
        return <FileText className={`${iconClasses} text-orange-500`} />;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'svg':
        return <FileText className={`${iconClasses} text-purple-500`} />;
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
        return <FileText className={`${iconClasses} text-pink-500`} />;
      case 'mp3':
      case 'wav':
      case 'flac':
        return <FileText className={`${iconClasses} text-yellow-500`} />;
      case 'zip':
      case 'rar':
      case '7z':
      case 'tar':
      case 'gz':
        return <FileText className={`${iconClasses} text-gray-500`} />;
      default:
        return <FileText className={`${iconClasses} text-primary`} />;
    }
  };

  // Transfers are already filtered and sorted by the backend

  if (loading) {
    return (
      <div className="min-h-screen bg-base-200 flex items-center justify-center" data-theme="fasttransfer">
        <div className="text-center">
          <span className="loading loading-spinner loading-lg text-primary"></span>
          <p className="mt-4 text-lg">Loading your transfers...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">{/* Embedded component - no header needed */}
        {/* Page Header */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-base-content mb-2">Transfer History</h2>
          <p className="text-base-content/70">Manage and download your transferred files</p>
        </div>

        {/* Stats Summary */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <div className="stat bg-base-100 rounded-2xl shadow-sm border border-base-300">
              <div className="stat-figure text-primary">
                <FileText className="w-8 h-8" />
              </div>
              <div className="stat-title">Total Transfers</div>
              <div className="stat-value text-primary">{stats.total}</div>
              <div className="stat-desc">{stats.recentActivity} in last 7 days</div>
            </div>

            <div className="stat bg-base-100 rounded-2xl shadow-sm border border-base-300">
              <div className="stat-figure text-success">
                <CheckCircle className="w-8 h-8" />
              </div>
              <div className="stat-title">Ready Files</div>
              <div className="stat-value text-success">{stats.byStatus.ready}</div>
              <div className="stat-desc">{stats.byStatus.compressing} compressing</div>
            </div>

            <div className="stat bg-base-100 rounded-2xl shadow-sm border border-base-300">
              <div className="stat-figure text-info">
                <LightningIcon className="w-8 h-8" usePng={true} />
              </div>
              <div className="stat-title">Avg Compression</div>
              <div className="stat-value text-info">
                {Math.round(stats.averageCompressionRatio * 100)}%
              </div>
              <div className="stat-desc">{Math.round((stats.totalSize - stats.totalCompressedSize) / 1024 / 1024)}MB saved</div>
            </div>

            <div className="stat bg-base-100 rounded-2xl shadow-sm border border-base-300">
              <div className="stat-figure text-secondary">
                <HardDrive className="w-8 h-8" />
              </div>
              <div className="stat-title">Storage Used</div>
              <div className="stat-value text-secondary">
                {Math.round(stats.storageUsed / 1024 / 1024)}MB
              </div>
              <div className="stat-desc">{stats.totalDownloads} total downloads</div>
            </div>
          </div>
        )}

        {error && (
          <div className="alert alert-error mb-6">
            <AlertCircle className="w-6 h-6" />
            <span>{error}</span>
            <button onClick={loadTransfers} className="btn btn-sm btn-outline">
              Retry
            </button>
          </div>
        )}

        {/* Controls Bar */}
        <div className="bg-base-100 rounded-2xl shadow-sm border border-base-300 p-6 mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Search and Filter */}
            <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3 flex-1">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-base-content/50" />
                <input
                  type="text"
                  placeholder="Search files..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="input input-bordered w-full pl-10"
                />
              </div>
              
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="select select-bordered"
              >
                <option value="all">All Status</option>
                <option value="ready">Ready</option>
                <option value="compressing">Compressing</option>
                <option value="uploading">Uploading</option>
                <option value="error">Error</option>
              </select>
            </div>

            {/* View Controls */}
            <div className="flex items-center space-x-3">
              <button
                onClick={handleRefresh}
                disabled={refreshing}
                className="btn btn-ghost btn-sm"
              >
                <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
              </button>
              
              <div className="join">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`btn join-item btn-sm ${viewMode === 'grid' ? 'btn-active' : 'btn-ghost'}`}
                >
                  <Grid className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`btn join-item btn-sm ${viewMode === 'list' ? 'btn-active' : 'btn-ghost'}`}
                >
                  <List className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          {/* Bulk Actions */}
          {selectedTransfers.size > 0 && (
            <div className="flex items-center justify-between mt-4 pt-4 border-t border-base-300">
              <span className="text-sm text-base-content/70">
                {selectedTransfers.size} file{selectedTransfers.size !== 1 ? 's' : ''} selected
              </span>
              <div className="flex space-x-2">
                <button
                  onClick={handleBulkDelete}
                  disabled={isDeleting}
                  className="btn btn-error btn-sm"
                >
                  {isDeleting ? (
                    <span className="loading loading-spinner loading-xs"></span>
                  ) : (
                    <Trash2 className="w-4 h-4" />
                  )}
                  {isDeleting ? 'Deleting...' : 'Delete Selected'}
                </button>
              </div>
            </div>
          )}
        </div>

        {/* File Browser */}
        {transfers.length === 0 ? (
          <div className="text-center py-16">
            <FileText className="w-16 h-16 text-base-content/30 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-base-content mb-2">
              {transfers.length === 0 ? 'No transfers yet' : 'No files match your search'}
            </h3>
            <p className="text-base-content/70 mb-6">
              {transfers.length === 0
                ? 'Upload your first file to get started'
                : 'Try adjusting your search or filter criteria'
              }
            </p>
            {transfers.length === 0 && (
              <button
                onClick={() => window.location.href = '/'}
                className="btn btn-primary"
              >
                Upload Files
              </button>
            )}
          </div>
        ) : (
          <>
            {/* Results Summary */}
            <div className="flex items-center justify-between mb-4">
              <p className="text-sm text-base-content/70">
                Showing {transfers.length} transfer{transfers.length !== 1 ? 's' : ''}
              </p>

              {viewMode === 'list' && (
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-base-content/70">Sort by:</span>
                  <div className="dropdown dropdown-end">
                    <div tabIndex={0} role="button" className="btn btn-ghost btn-sm">
                      {sortField.charAt(0).toUpperCase() + sortField.slice(1)}
                      <ArrowUpDown className="w-3 h-3 ml-1" />
                    </div>
                    <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-40">
                      <li><button onClick={() => handleSort('filename')}>Name</button></li>
                      <li><button onClick={() => handleSort('size')}>Size</button></li>
                      <li><button onClick={() => handleSort('createdAt')}>Date</button></li>
                      <li><button onClick={() => handleSort('status')}>Status</button></li>
                    </ul>
                  </div>
                </div>
              )}
            </div>

            {/* Grid View */}
            {viewMode === 'grid' && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {transfers.map((transfer) => (
                  <div key={transfer.transferId} className="card bg-base-100 shadow-sm border border-base-300 hover:shadow-md transition-shadow">
                    <div className="card-body p-4">
                      {/* Selection Checkbox */}
                      <div className="flex items-start justify-between mb-3">
                        <input
                          type="checkbox"
                          checked={selectedTransfers.has(transfer.transferId)}
                          onChange={() => toggleSelectTransfer(transfer.transferId)}
                          className="checkbox checkbox-sm"
                        />
                        <div className="dropdown dropdown-end">
                          <div tabIndex={0} role="button" className="btn btn-ghost btn-xs">
                            <MoreVertical className="w-4 h-4" />
                          </div>
                          <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-40">
                            <li><button onClick={() => setSelectedFileDetails(transfer)}>
                              <Info className="w-4 h-4" />
                              Details
                            </button></li>
                            {transfer.status === 'ready' && transfer.downloadUrl && (
                              <>
                                <li><button onClick={() => handleDownload(transfer, 'compressed')}>
                                  <Archive className="w-4 h-4" />
                                  Download Compressed (.zmt)
                                </button></li>
                                <li><button onClick={() => handleDownload(transfer, 'original')}>
                                  <File className="w-4 h-4" />
                                  Download Original
                                </button></li>
                              </>
                            )}
                            <li><button onClick={() => handleDelete(transfer.transferId)}>
                              <Trash2 className="w-4 h-4" />
                              Delete
                            </button></li>
                          </ul>
                        </div>
                      </div>

                      {/* File Icon and Info */}
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="p-2 bg-primary/10 rounded-lg">
                          {getFileIcon(transfer.filename)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3
                            className="font-medium text-base-content truncate cursor-pointer hover:text-primary transition-colors"
                            title={transfer.filename}
                            onClick={() => setSelectedFileDetails(transfer)}
                          >
                            {transfer.filename}
                          </h3>
                          <p className="text-sm text-base-content/70">{formatFileSize(transfer.size)}</p>
                        </div>
                      </div>

                      {/* Status and Compression */}
                      <div className="flex items-center justify-between mb-3">
                        <div className={getStatusBadge(transfer.status)}>
                          {getStatusIcon(transfer.status)}
                          <span className="ml-1 capitalize">{transfer.status}</span>
                        </div>
                        {transfer.compressionRatio && (
                          <div className="badge badge-success badge-sm">
                            <LightningIcon className="w-3 h-3 mr-1" usePng={true} />
                            {Math.round(transfer.compressionRatio * 100)}%
                          </div>
                        )}
                      </div>

                      {/* Date */}
                      <p className="text-xs text-base-content/50 mb-3">
                        {formatDate(transfer.createdAt)}
                      </p>

                      {/* Actions */}
                      {transfer.status === 'ready' && transfer.downloadUrl && (
                        <div className="dropdown dropdown-top w-full">
                          <div tabIndex={0} role="button" className="btn btn-primary btn-sm w-full">
                            <Download className="w-4 h-4" />
                            Download
                          </div>
                          <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-full">
                            <li><button onClick={() => handleDownload(transfer, 'compressed')} className="w-full justify-start">
                              <Archive className="w-4 h-4" />
                              Compressed (.zmt)
                            </button></li>
                            <li><button onClick={() => handleDownload(transfer, 'original')} className="w-full justify-start">
                              <File className="w-4 h-4" />
                              Original File
                            </button></li>
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* List View */}
            {viewMode === 'list' && (
              <div className="bg-base-100 rounded-2xl shadow-sm border border-base-300 overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="table table-zebra">
                    <thead>
                      <tr>
                        <th>
                          <input
                            type="checkbox"
                            checked={selectedTransfers.size === transfers.length && transfers.length > 0}
                            onChange={toggleSelectAll}
                            className="checkbox"
                          />
                        </th>
                        <th>
                          <button
                            onClick={() => handleSort('filename')}
                            className="flex items-center space-x-1 hover:text-primary"
                          >
                            <span>Name</span>
                            <ArrowUpDown className="w-3 h-3" />
                          </button>
                        </th>
                        <th>
                          <button
                            onClick={() => handleSort('size')}
                            className="flex items-center space-x-1 hover:text-primary"
                          >
                            <span>Size</span>
                            <ArrowUpDown className="w-3 h-3" />
                          </button>
                        </th>
                        <th>Status</th>
                        <th>Compression</th>
                        <th>
                          <button
                            onClick={() => handleSort('createdAt')}
                            className="flex items-center space-x-1 hover:text-primary"
                          >
                            <span>Date</span>
                            <ArrowUpDown className="w-3 h-3" />
                          </button>
                        </th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {transfers.map((transfer) => (
                        <tr key={transfer.transferId} className="hover">
                          <td>
                            <input
                              type="checkbox"
                              checked={selectedTransfers.has(transfer.transferId)}
                              onChange={() => toggleSelectTransfer(transfer.transferId)}
                              className="checkbox"
                            />
                          </td>
                          <td>
                            <div className="flex items-center space-x-3">
                              <div className="p-2 bg-primary/10 rounded-lg">
                                {getFileIcon(transfer.filename)}
                              </div>
                              <div>
                                <div
                                  className="font-medium text-base-content cursor-pointer hover:text-primary transition-colors"
                                  onClick={() => setSelectedFileDetails(transfer)}
                                >
                                  {transfer.filename}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="text-base-content/70">{formatFileSize(transfer.size)}</td>
                          <td>
                            <div className={getStatusBadge(transfer.status)}>
                              {getStatusIcon(transfer.status)}
                              <span className="ml-1 capitalize">{transfer.status}</span>
                            </div>
                          </td>
                          <td>
                            {transfer.compressionRatio ? (
                              <div className="badge badge-success badge-sm">
                                <LightningIcon className="w-3 h-3 mr-1" usePng={true} />
                                {Math.round(transfer.compressionRatio * 100)}%
                              </div>
                            ) : (
                              <span className="text-base-content/50">-</span>
                            )}
                          </td>
                          <td className="text-base-content/70">{formatDate(transfer.createdAt)}</td>
                          <td>
                            <div className="flex space-x-2">
                              {transfer.status === 'ready' && transfer.downloadUrl && (
                                <div className="dropdown dropdown-end">
                                  <div tabIndex={0} role="button" className="btn btn-primary btn-xs">
                                    <Download className="w-3 h-3" />
                                  </div>
                                  <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-48">
                                    <li><button onClick={() => handleDownload(transfer, 'compressed')}>
                                      <Archive className="w-4 h-4" />
                                      Compressed (.zmt)
                                    </button></li>
                                    <li><button onClick={() => handleDownload(transfer, 'original')}>
                                      <File className="w-4 h-4" />
                                      Original File
                                    </button></li>
                                  </ul>
                                </div>
                              )}
                              <button
                                onClick={() => handleDelete(transfer.transferId)}
                                className="btn btn-error btn-xs"
                              >
                                <Trash2 className="w-3 h-3" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </>
        )}

        {/* File Details Modal */}
        {selectedFileDetails && (
          <div className="modal modal-open">
            <div className="modal-box max-w-2xl">
              <div className="flex items-center justify-between mb-6">
                <h3 className="font-bold text-lg">File Details</h3>
                <button
                  onClick={() => setSelectedFileDetails(null)}
                  className="btn btn-sm btn-circle btn-ghost"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>

              <div className="space-y-6">
                {/* File Header */}
                <div className="flex items-center space-x-4">
                  <div className="p-3 bg-primary/10 rounded-xl">
                    {getFileIcon(selectedFileDetails.filename)}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-lg text-base-content">{selectedFileDetails.filename}</h4>
                    <p className="text-base-content/70">{formatFileSize(selectedFileDetails.size)}</p>
                  </div>
                  <div className={getStatusBadge(selectedFileDetails.status)}>
                    {getStatusIcon(selectedFileDetails.status)}
                    <span className="ml-1 capitalize">{selectedFileDetails.status}</span>
                  </div>
                </div>

                {/* File Stats */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="stat bg-base-200 rounded-xl p-4">
                    <div className="stat-title text-sm">Upload Date</div>
                    <div className="stat-value text-base text-base-content">
                      {formatDate(selectedFileDetails.createdAt)}
                    </div>
                  </div>

                  {selectedFileDetails.compressionRatio && (
                    <div className="stat bg-base-200 rounded-xl p-4">
                      <div className="stat-title text-sm">Compression</div>
                      <div className="stat-value text-base text-success">
                        {Math.round(selectedFileDetails.compressionRatio * 100)}%
                      </div>
                    </div>
                  )}

                  <div className="stat bg-base-200 rounded-xl p-4">
                    <div className="stat-title text-sm">Transfer ID</div>
                    <div className="stat-value text-xs text-base-content font-mono">
                      {selectedFileDetails.transferId}
                    </div>
                  </div>

                  {selectedFileDetails.compressionRatio && (
                    <div className="stat bg-base-200 rounded-xl p-4">
                      <div className="stat-title text-sm">Space Saved</div>
                      <div className="stat-value text-base text-success">
                        {formatFileSize(selectedFileDetails.size * selectedFileDetails.compressionRatio)}
                      </div>
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="flex space-x-3">
                  {selectedFileDetails.status === 'ready' && selectedFileDetails.downloadUrl && (
                    <div className="dropdown dropdown-top flex-1">
                      <div tabIndex={0} role="button" className="btn btn-primary w-full">
                        <Download className="w-4 h-4" />
                        Download File
                      </div>
                      <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-full">
                        <li><button onClick={() => {
                          handleDownload(selectedFileDetails, 'compressed');
                          setSelectedFileDetails(null);
                        }} className="w-full justify-start">
                          <Archive className="w-4 h-4" />
                          Compressed (.zmt)
                        </button></li>
                        <li><button onClick={() => {
                          handleDownload(selectedFileDetails, 'original');
                          setSelectedFileDetails(null);
                        }} className="w-full justify-start">
                          <File className="w-4 h-4" />
                          Original File
                        </button></li>
                      </ul>
                    </div>
                  )}
                  <button
                    onClick={() => {
                      handleDelete(selectedFileDetails.transferId);
                      setSelectedFileDetails(null);
                    }}
                    className="btn btn-error"
                  >
                    <Trash2 className="w-4 h-4" />
                    Delete
                  </button>
                </div>
              </div>
            </div>
            <div className="modal-backdrop" onClick={() => setSelectedFileDetails(null)}></div>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center mt-8">
            <div className="join">
              <button
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
                className="join-item btn btn-sm"
              >
                Previous
              </button>

              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                return (
                  <button
                    key={pageNum}
                    onClick={() => setCurrentPage(pageNum)}
                    className={`join-item btn btn-sm ${currentPage === pageNum ? 'btn-active' : ''}`}
                  >
                    {pageNum}
                  </button>
                );
              })}

              <button
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
                className="join-item btn btn-sm"
              >
                Next
              </button>
            </div>
          </div>
        )}
    </div>
  );
}
