import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, Download, FileText, Zap, CheckCircle, AlertCircle, Clock, Archive, Share2, Link, Copy, X, Mail, BarChart3, File } from 'lucide-react';
import { LightningIcon } from './components/LightningIcon';
import { uploadFile, downloadCompressedFile, downloadOriginalFile, getTransferStatus, decompressFile, downloadDecompressedFile, generateShareLink, sendShareEmail, validateUpload } from './services/api';
import type { LinkRequest, EmailRequest, ValidateUploadRequest } from './services/api';
import { useProgressTracking } from './hooks/useProgressTracking';
import { ProgressTracker } from './components/ProgressTracker';
import { AnalyticsDashboard } from './components/AnalyticsDashboard';
import Downloads from './components/Downloads';

interface Transfer {
  id: string;
  filename: string;
  size: number;
  status: 'uploading' | 'compressing' | 'ready' | 'error';
  downloadUrl?: string;
  compressionRatio?: number;
}

interface DecompressedFile {
  decompressId: string;
  filename: string;
  originalFilename: string;
  downloadUrl: string;
}

function App() {
  const [transfers, setTransfers] = useState<Transfer[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [decompressedFiles, setDecompressedFiles] = useState<DecompressedFile[]>([]);
  const [isDecompressing, setIsDecompressing] = useState(false);
  const [shareModalOpen, setShareModalOpen] = useState(false);
  const [selectedTransfer, setSelectedTransfer] = useState<Transfer | null>(null);
  const [shareUrl, setShareUrl] = useState('');
  const [shareSettings, setShareSettings] = useState({
    expirationHours: 24,
    downloadLimit: 0,
    password: ''
  });
  const [emailData, setEmailData] = useState({
    recipientEmail: '',
    senderName: ''
  });
  const [isSendingEmail, setIsSendingEmail] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [activeTab, setActiveTab] = useState<'upload' | 'analytics' | 'transfers'>('upload');

  // Email capture for account creation
  const [userEmail, setUserEmail] = useState('');
  const [showEmailCapture, setShowEmailCapture] = useState(false);
  const [emailCaptured, setEmailCaptured] = useState(false);

  // Progress tracking for current upload
  const progressTracking = useProgressTracking();

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    const file = acceptedFiles[0];
    const transferId = Date.now().toString();

    const newTransfer: Transfer = {
      id: transferId,
      filename: file.name,
      size: file.size,
      status: 'uploading'
    };

    setTransfers(prev => [...prev, newTransfer]);
    setIsUploading(true);

    // Start progress tracking
    progressTracking.startTracking(file.size);

    try {
      await uploadFile(file, transferId, (bytesUploaded, _totalBytes) => {
        progressTracking.updateUploadProgress(bytesUploaded);
      });

      setTransfers(prev => prev.map(t =>
        t.id === transferId ? { ...t, status: 'compressing' } : t
      ));

      // Start compression tracking
      progressTracking.startCompression();

      const pollStatus = async () => {
        try {
          const status = await getTransferStatus(transferId);
          if (status.status === 'ready') {
            setTransfers(prev => prev.map(t =>
              t.id === transferId ? {
                ...t,
                status: 'ready',
                downloadUrl: status.downloadUrl,
                compressionRatio: status.compressionRatio
              } : t
            ));
            // Mark compression complete
            progressTracking.markComplete(status.compressionRatio);
          } else if (status.status === 'error') {
            setTransfers(prev => prev.map(t =>
              t.id === transferId ? { ...t, status: 'error' } : t
            ));
            progressTracking.markError('Compression failed');
          } else {
            setTimeout(pollStatus, 2000);
          }
        } catch (error) {
          console.error('Error polling status:', error);
          setTransfers(prev => prev.map(t =>
            t.id === transferId ? { ...t, status: 'error' } : t
          ));
          progressTracking.markError('Status check failed');
        }
      };

      setTimeout(pollStatus, 1000);
    } catch (error) {
      console.error('Upload failed:', error);
      setTransfers(prev => prev.map(t =>
        t.id === transferId ? { ...t, status: 'error' } : t
      ));
      progressTracking.markError('Upload failed');
    } finally {
      setIsUploading(false);
    }
  }, [progressTracking]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: false,
    disabled: isUploading
  });

  const handleDownload = async (transfer: Transfer, downloadType: 'compressed' | 'original' = 'compressed') => {
    try {
      if (downloadType === 'original') {
        await downloadOriginalFile(transfer.id, transfer.filename);
      } else {
        await downloadCompressedFile(transfer.id, transfer.filename);
      }
    } catch (error) {
      console.error('Download failed:', error);
      alert(`Failed to download ${downloadType} file`);
    }
  };

  const handleShare = (transfer: Transfer) => {
    setSelectedTransfer(transfer);
    setShareModalOpen(true);
    setShareUrl('');
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusIcon = (status: Transfer['status']) => {
    switch (status) {
      case 'uploading':
        return <LightningIcon className="w-4 h-4 text-primary animate-pulse" usePng={true} />;
      case 'compressing':
        return <LightningIcon className="w-4 h-4 text-yellow-500 animate-pulse" usePng={true} />;
      case 'ready':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
    }
  };

  const getStatusText = (status: Transfer['status']) => {
    switch (status) {
      case 'uploading':
        return 'Uploading';
      case 'compressing':
        return 'Compressing';
      case 'ready':
        return 'Ready';
      case 'error':
        return 'Error';
    }
  };

  // Decompression functionality
  const onDecompressDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    setIsDecompressing(true);

    try {
      for (const file of acceptedFiles) {
        const decompressId = Date.now().toString() + Math.random().toString(36).substr(2, 9);

        const result = await decompressFile(file, decompressId);

        if (result.files && result.files.length > 0) {
          const newDecompressedFiles = result.files.map((f: any) => ({
            decompressId,
            filename: f.filename,
            originalFilename: file.name,
            downloadUrl: f.downloadUrl
          }));

          setDecompressedFiles(prev => [...prev, ...newDecompressedFiles]);
        }
      }
    } catch (error) {
      console.error('Decompression failed:', error);
    } finally {
      setIsDecompressing(false);
    }
  }, []);

  const { getRootProps: getDecompressRootProps, getInputProps: getDecompressInputProps, isDragActive: isDecompressDragActive } = useDropzone({
    onDrop: onDecompressDrop,
    multiple: true,
    disabled: isDecompressing,
    accept: {
      'application/octet-stream': ['.zmt']
    }
  });

  const handleDecompressedDownload = async (decompressedFile: DecompressedFile) => {
    try {
      await downloadDecompressedFile(decompressedFile.downloadUrl, decompressedFile.filename);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  // Share modal functions
  const generateShare = async () => {
    if (!selectedTransfer) return;

    try {
      const linkRequest: LinkRequest = {
        transferId: selectedTransfer.id,
        expirationHours: shareSettings.expirationHours,
        downloadLimit: shareSettings.downloadLimit,
        password: shareSettings.password || undefined
      };

      const response = await generateShareLink(linkRequest);
      setShareUrl(response.shareUrl);
    } catch (error) {
      console.error('Failed to generate share link:', error);
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const closeShareModal = () => {
    setShareModalOpen(false);
    setSelectedTransfer(null);
    setShareUrl('');
    setShareSettings({
      expirationHours: 24,
      downloadLimit: 0,
      password: ''
    });
    setEmailData({
      recipientEmail: '',
      senderName: ''
    });
    setIsSendingEmail(false);
    setEmailSent(false);
  };

  const sendEmail = async () => {
    if (!selectedTransfer || !emailData.recipientEmail) return;

    try {
      setIsSendingEmail(true);

      const emailRequest: EmailRequest = {
        transferId: selectedTransfer.id,
        recipientEmail: emailData.recipientEmail,
        senderName: emailData.senderName || undefined
      };

      const response = await sendShareEmail(emailRequest);

      if (response.success) {
        setEmailSent(true);
      } else {
        console.error('Failed to send email:', response.error);
        alert('Failed to send email. Please try again.');
      }
    } catch (error) {
      console.error('Failed to send email:', error);
      alert('Failed to send email. Please try again.');
    } finally {
      setIsSendingEmail(false);
    }
  };

  return (
    <div className="min-h-screen bg-base-200" data-theme="fasttransfer">
      {/* Header */}
      <header className="bg-base-100 border-b border-base-300">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Logo and Brand */}
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-white rounded-xl">
                <div className="w-8 h-8 bg-black rounded flex items-center justify-center">
                  <span className="text-white font-bold text-sm">⚡</span>
                </div>
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">upload-1</h1>
              </div>
            </div>

            {/* Navigation */}
            <nav className="hidden md:flex items-center space-x-6">
              <button className="btn btn-ghost text-white hover:text-primary">
                Sign In
              </button>
              <button className="btn btn-primary btn-sm rounded-lg">
                Get Started
              </button>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Tab Navigation */}
        <div className="tabs tabs-boxed mb-8 bg-base-200 p-1 rounded-2xl">
          <button
            className={`tab tab-lg flex-1 ${activeTab === 'upload' ? 'tab-active' : ''}`}
            onClick={() => setActiveTab('upload')}
          >
            <Upload className="w-5 h-5 mr-2" />
            File Transfer
          </button>
          <button
            className={`tab tab-lg flex-1 ${activeTab === 'transfers' ? 'tab-active' : ''}`}
            onClick={() => setActiveTab('transfers')}
          >
            <Download className="w-5 h-5 mr-2" />
            Transfer History
          </button>
          <button
            className={`tab tab-lg flex-1 ${activeTab === 'analytics' ? 'tab-active' : ''}`}
            onClick={() => setActiveTab('analytics')}
          >
            <BarChart3 className="w-5 h-5 mr-2" />
            Analytics
          </button>
        </div>

        {activeTab === 'upload' && (
          <>
            {/* Hero Section */}
            <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          {/* Left Side - Hero Text */}
          <div className="space-y-8">
            <div className="space-y-6">
              <h1 className="text-5xl lg:text-6xl font-bold text-white leading-tight">
                We'll <span className="text-primary">shrink</span> it,<br />
                you <span className="text-primary">share</span> it.
              </h1>

              {/* Feature List */}
              <div className="space-y-3 text-white/80">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span>Up to 80% compression on most files.</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span>Undetectable quality loss, seriously.</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span>No tiny limits, send up to 1TB for free.</span>
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Upload Area */}
          <div className="relative">
            <div
              {...getRootProps()}
              className={`
                bg-base-200 rounded-3xl p-8 border-2 border-dashed cursor-pointer transition-all duration-300
                ${isDragActive
                  ? 'border-primary bg-primary/10 scale-105'
                  : 'border-base-300 hover:border-primary/50'
                }
                ${isUploading ? 'pointer-events-none opacity-75' : ''}
              `}
            >
              <input {...getInputProps()} />
              <div className="text-center space-y-6">
                <div className={`w-16 h-16 mx-auto rounded-full flex items-center justify-center transition-all duration-300 ${
                  isDragActive ? 'bg-primary scale-110' : 'bg-base-300'
                }`}>
                  <LightningIcon className={`w-8 h-8 ${isDragActive ? 'text-white' : 'text-white/60'}`} usePng={true} />
                </div>

                {isDragActive ? (
                  <div>
                    <p className="text-lg font-semibold text-primary">Drop it here!</p>
                    <p className="text-sm text-white/60">Release to upload your file</p>
                  </div>
                ) : (
                  <div>
                    <p className="text-lg font-semibold text-white">Drag and drop a file</p>
                    <p className="text-sm text-white/60">Or search for a file</p>
                  </div>
                )}

                {!isDragActive && !isUploading && (
                  <button className="btn btn-primary btn-lg rounded-xl px-8">
                    Choose File
                  </button>
                )}

                {isUploading && (
                  <div className="flex items-center justify-center space-x-3 text-primary">
                    <span className="loading loading-spinner loading-md"></span>
                    <span className="font-medium">Uploading your file...</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Why we're better section */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-white mb-4">Why we're better</h2>
            <p className="text-white/70 text-lg max-w-2xl mx-auto">
              We didn't just build another file transfer service. We revolutionized it with
              insane compression technology.
            </p>
          </div>

          {/* Comparison Table */}
          <div className="bg-base-200 rounded-2xl overflow-hidden">
            <table className="w-full">
              <thead>
                <tr className="border-b border-base-300">
                  <th className="text-left p-4 text-white/70 font-medium">Feature</th>
                  <th className="text-center p-4 text-primary font-bold">Yeehaw!</th>
                  <th className="text-center p-4 text-white/70 font-medium">WeTransfer</th>
                  <th className="text-center p-4 text-white/70 font-medium">Others</th>
                </tr>
              </thead>
              <tbody className="text-sm">
                <tr className="border-b border-base-300">
                  <td className="p-4 text-white/70">File size limit</td>
                  <td className="p-4 text-center text-primary font-bold">● 1TB</td>
                  <td className="p-4 text-center text-white/70">2 GB</td>
                  <td className="p-4 text-center text-white/70">100 MB</td>
                </tr>
                <tr className="border-b border-base-300">
                  <td className="p-4 text-white/70">Compression rate</td>
                  <td className="p-4 text-center text-primary font-bold">● 80% on average</td>
                  <td className="p-4 text-center text-white/70">None</td>
                  <td className="p-4 text-center text-white/70">Basic</td>
                </tr>
                <tr className="border-b border-base-300">
                  <td className="p-4 text-white/70">Upload speed</td>
                  <td className="p-4 text-center text-primary font-bold">⚡ Lightning fast</td>
                  <td className="p-4 text-center text-white/70">Slow</td>
                  <td className="p-4 text-center text-white/70">Slower</td>
                </tr>
                <tr className="border-b border-base-300">
                  <td className="p-4 text-white/70">Account required</td>
                  <td className="p-4 text-center text-primary font-bold">Nope!</td>
                  <td className="p-4 text-center text-white/70">No</td>
                  <td className="p-4 text-center text-white/70">Yes</td>
                </tr>
                <tr>
                  <td className="p-4 text-white/70">Ads</td>
                  <td className="p-4 text-center text-primary font-bold">Nope!</td>
                  <td className="p-4 text-center text-white/70">Yes</td>
                  <td className="p-4 text-center text-white/70">Yes</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Decompression Section */}









        <div className="mb-12">
          <div className="bg-base-100 rounded-3xl shadow-xl border border-base-300">
          <div className="p-8">
            <h2 className="text-3xl font-bold mb-2 flex items-center space-x-3">
              <div className="p-2 bg-floppy-orange rounded-xl">
                <Archive className="w-6 h-6 text-white" />
              </div>
              <span>Decompress ZMT Files</span>
            </h2>
            <p className="text-base-content/70 mb-8">
              Already have a ZMT compressed file? Upload it here to extract the original files.
            </p>

            <div
              {...getDecompressRootProps()}
              className={`
                border-2 border-dashed rounded-3xl p-8 text-center cursor-pointer transition-all duration-300
                ${isDecompressDragActive
                  ? 'border-floppy-orange bg-orange-50 scale-105'
                  : 'border-orange-300 hover:border-floppy-orange hover:bg-orange-50/50'
                }
                ${isDecompressing ? 'pointer-events-none opacity-75' : ''}
              `}
            >
              <input {...getDecompressInputProps()} />
              <div className="flex flex-col items-center space-y-6">
                <div className={`p-6 rounded-full transition-all duration-300 ${
                  isDecompressDragActive ? 'bg-floppy-orange scale-110' : 'bg-orange-100'
                }`}>
                  <Archive className={`w-16 h-16 ${isDecompressDragActive ? 'text-white' : 'text-floppy-orange'}`} />
                </div>
                {isDecompressDragActive ? (
                  <div className="text-center">
                    <p className="text-2xl font-bold text-floppy-orange">Ready to decompress!</p>
                    <p className="text-base-content/70">Release to extract your files</p>
                  </div>
                ) : (
                  <div className="text-center space-y-4">
                    <div>
                      <p className="text-2xl font-bold text-base-content mb-2">
                        Drop your ZMT files here
                      </p>
                      <p className="text-base-content/70">or</p>
                    </div>
                    <button className="btn bg-floppy-orange hover:bg-floppy-orange/80 text-white border-0 rounded-2xl">
                      Browse ZMT Files
                    </button>
                    <div className="badge bg-orange-100 text-floppy-orange border-floppy-orange/30 font-medium">
                      .zmt files only
                    </div>
                  </div>
                )}
                {isDecompressing && (
                  <div className="flex items-center space-x-3 text-floppy-orange">
                    <span className="loading loading-spinner loading-md"></span>
                    <span className="font-medium">Decompressing your files...</span>
                  </div>
                )}
              </div>
            </div>
          </div>
          </div>
        </div>

        {/* Decompressed Files List */}
        {decompressedFiles.length > 0 && (
          <div className="bg-base-100 rounded-3xl shadow-xl border border-base-300 mt-8">
            <div className="p-8">
              <h3 className="text-2xl font-bold mb-6 flex items-center space-x-3">
                <div className="p-2 bg-floppy-green rounded-xl">
                  <CheckCircle className="w-5 h-5 text-white" />
                </div>
                <span>Decompressed Files</span>
              </h3>
              <div className="space-y-4">
                {decompressedFiles.map((file, index) => (
                  <div key={`${file.decompressId}-${index}`} className="bg-green-50 border border-green-200 rounded-2xl p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="p-2 bg-floppy-green rounded-xl">
                          <FileText className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <p className="font-bold text-base-content">{file.filename}</p>
                          <p className="text-sm text-base-content/70">From: {file.originalFilename}</p>
                        </div>
                      </div>
                      <button
                        onClick={() => handleDecompressedDownload(file)}
                        className="btn bg-floppy-green hover:bg-floppy-green/80 text-white border-0 rounded-2xl btn-sm"
                      >
                        <Download className="w-4 h-4" />
                        Download
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Progress Tracker */}
        {(progressTracking.progress.phase !== 'idle' && transfers.length > 0) && (
          <div className="mb-8">
            <ProgressTracker
              progress={progressTracking.progress}
              fileName={transfers[transfers.length - 1]?.filename || 'Unknown'}
              fileSize={transfers[transfers.length - 1]?.size || 0}
              formatSpeed={progressTracking.formatSpeed}
              formatTime={progressTracking.formatTime}
              formatFileSize={progressTracking.formatFileSize}
            />
          </div>
        )}

        {/* Transfer List */}
        {transfers.length > 0 && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-3xl font-bold text-base-content">Your Transfers</h2>
              <div className="badge badge-primary badge-lg">
                {transfers.length} file{transfers.length !== 1 ? 's' : ''}
              </div>
            </div>

            <div className="grid gap-6">
              {transfers.map((transfer) => (
                <div key={transfer.id} className="bg-base-100 rounded-3xl shadow-xl border border-base-300 overflow-hidden">
                  <div className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-4">
                        <div className="avatar placeholder">
                          <div className="bg-primary text-primary-content rounded-full w-12">
                            <FileText className="w-6 h-6" />
                          </div>
                        </div>
                        <div>
                          <h3 className="card-title text-lg">{transfer.filename}</h3>
                          <div className="flex items-center space-x-3 text-sm">
                            <span className="text-base-content/70">{formatFileSize(transfer.size)}</span>
                            {transfer.compressionRatio && (
                              <div className="flex items-center space-x-2">
                                <div className="divider divider-horizontal"></div>
                                <div className="badge badge-success gap-1 font-bold">
                                  <Zap className="w-3 h-3" />
                                  {Math.round(transfer.compressionRatio * 100)}% COMPRESSED
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-3">
                        <div className={`badge badge-lg gap-2 font-bold ${
                          transfer.status === 'ready' ? 'bg-floppy-green text-white' :
                          transfer.status === 'compressing' ? 'bg-floppy-yellow text-black' :
                          transfer.status === 'uploading' ? 'bg-floppy-blue text-white' :
                          'bg-red-500 text-white'
                        }`}>
                          {getStatusIcon(transfer.status)}
                          {getStatusText(transfer.status)}
                        </div>

                        {transfer.status === 'ready' && transfer.downloadUrl && (
                          <div className="flex space-x-3">
                            <div className="dropdown dropdown-top">
                              <div tabIndex={0} role="button" className="btn bg-floppy-blue hover:bg-floppy-blue/80 text-white border-0 rounded-2xl btn-sm">
                                <Download className="w-4 h-4" />
                                Download
                              </div>
                              <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                                <li><button onClick={() => handleDownload(transfer, 'compressed')}>
                                  <Archive className="w-4 h-4" />
                                  Compressed (.zmt)
                                </button></li>
                                <li><button onClick={() => handleDownload(transfer, 'original')}>
                                  <File className="w-4 h-4" />
                                  Original File
                                </button></li>
                              </ul>
                            </div>
                            <button
                              onClick={() => handleShare(transfer)}
                              className="btn bg-floppy-purple hover:bg-floppy-purple/80 text-white border-0 rounded-2xl btn-sm"
                            >
                              <Share2 className="w-4 h-4" />
                              Share
                            </button>
                          </div>
                        )}
                      </div>
                    </div>

                    {transfer.status === 'compressing' && (
                      <div className="mt-4">
                        <div className="flex items-center space-x-2 text-sm text-base-content opacity-70 mb-2">
                          <Clock className="w-4 h-4" />
                          <span>Compressing with ZMT technology...</span>
                        </div>
                        <progress className="progress progress-warning w-full"></progress>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Compression Showcase */}
        {transfers.some(t => t.compressionRatio) && (
          <div className="bg-gradient-to-r from-floppy-green/10 to-floppy-blue/10 rounded-3xl p-8 mb-8 border border-floppy-green/20">
            <div className="text-center space-y-4">
              <h3 className="text-2xl font-bold text-base-content">🚀 Compression Magic in Action</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-4xl font-black text-floppy-green">
                    {transfers.filter(t => t.compressionRatio).length > 0
                      ? Math.round(transfers.filter(t => t.compressionRatio)[0].compressionRatio! * 100)
                      : 97.5}%
                  </div>
                  <div className="text-sm text-base-content/70">Compression Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-black text-floppy-blue">
                    {transfers.filter(t => t.status === 'ready').length}
                  </div>
                  <div className="text-sm text-base-content/70">Files Compressed</div>
                </div>
                <div className="text-center">
                  <div className="text-4xl font-black text-floppy-purple">
                    {Math.round(transfers.reduce((acc, t) => acc + (t.compressionRatio ? (t.size * t.compressionRatio) : 0), 0) / 1024 / 1024)}MB
                  </div>
                  <div className="text-sm text-base-content/70">Space Saved</div>
                </div>
              </div>
            </div>
          </div>
        )}
        {/* It's not snake oil section */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-white mb-4">It's not snake oil</h2>
            <p className="text-white/70 text-lg max-w-2xl mx-auto">
              We've cracked the code on file compression. Our proprietary algorithm makes
              files up to 80% smaller without losing quality. Yes, really.
            </p>
          </div>

          {/* Compression Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-base-200 rounded-2xl p-6 text-center">
              <div className="text-sm text-white/60 mb-2 flex items-center justify-center space-x-2">
                <File className="w-4 h-4" />
                <span>4K video files</span>
              </div>
              <div className="text-4xl font-bold text-primary mb-2">79%</div>
              <div className="text-sm text-white/60">Avg. compression rate</div>
            </div>
            <div className="bg-base-200 rounded-2xl p-6 text-center">
              <div className="text-sm text-white/60 mb-2 flex items-center justify-center space-x-2">
                <File className="w-4 h-4" />
                <span>4K video files</span>
              </div>
              <div className="text-4xl font-bold text-primary mb-2">79%</div>
              <div className="text-sm text-white/60">Avg. compression rate</div>
            </div>
            <div className="bg-base-200 rounded-2xl p-6 text-center">
              <div className="text-sm text-white/60 mb-2 flex items-center justify-center space-x-2">
                <File className="w-4 h-4" />
                <span>4K video files</span>
              </div>
              <div className="text-4xl font-bold text-primary mb-2">79%</div>
              <div className="text-sm text-white/60">Avg. compression rate</div>
            </div>
            <div className="bg-base-200 rounded-2xl p-6 text-center">
              <div className="text-sm text-white/60 mb-2 flex items-center justify-center space-x-2">
                <File className="w-4 h-4" />
                <span>4K video files</span>
              </div>
              <div className="text-4xl font-bold text-primary mb-2">79%</div>
              <div className="text-sm text-white/60">Avg. compression rate</div>
            </div>
          </div>
        </div>
          </>
        )}

        {/* Transfer History Tab */}
        {activeTab === 'transfers' && (
          <div className="bg-base-100 rounded-3xl shadow-xl border border-base-300 p-8">
            <Downloads />
          </div>
        )}

        {/* Analytics Tab */}
        {activeTab === 'analytics' && (
          <AnalyticsDashboard className="mt-8" />
        )}

        {/* Share Modal */}
        {shareModalOpen && (
          <div className="modal modal-open">
            <div className="modal-box max-w-md rounded-3xl">
              <div className="flex items-center justify-between mb-6">
                <h3 className="font-bold text-2xl flex items-center space-x-3">
                  <div className="p-2 bg-floppy-purple rounded-xl">
                    <Share2 className="w-6 h-6 text-white" />
                  </div>
                  <span>Share Transfer</span>
                </h3>
                <button
                  onClick={closeShareModal}
                  className="btn btn-sm btn-circle bg-base-200 hover:bg-base-300 border-0"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>

              {selectedTransfer && (
                <div className="alert alert-info mb-6">
                  <FileText className="w-5 h-5" />
                  <div>
                    <div className="font-bold">File to share</div>
                    <div className="text-sm">{selectedTransfer.filename}</div>
                  </div>
                </div>
              )}

              {!shareUrl ? (
                <div className="space-y-6">
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text font-medium">Expiration Time</span>
                      <span className="label-text-alt">1-168 hours</span>
                    </label>
                    <input
                      type="number"
                      value={shareSettings.expirationHours}
                      onChange={(e) => setShareSettings(prev => ({ ...prev, expirationHours: parseInt(e.target.value) || 24 }))}
                      className="input input-bordered w-full"
                      min="1"
                      max="168"
                      placeholder="24"
                    />
                  </div>

                  <div className="form-control">
                    <label className="label">
                      <span className="label-text font-medium">Download Limit</span>
                      <span className="label-text-alt">0 = unlimited</span>
                    </label>
                    <input
                      type="number"
                      value={shareSettings.downloadLimit}
                      onChange={(e) => setShareSettings(prev => ({ ...prev, downloadLimit: parseInt(e.target.value) || 0 }))}
                      className="input input-bordered w-full"
                      min="0"
                      placeholder="0"
                    />
                  </div>

                  <div className="form-control">
                    <label className="label">
                      <span className="label-text font-medium">Password Protection</span>
                      <span className="label-text-alt">Optional</span>
                    </label>
                    <input
                      type="password"
                      value={shareSettings.password}
                      onChange={(e) => setShareSettings(prev => ({ ...prev, password: e.target.value }))}
                      className="input input-bordered w-full"
                      placeholder="Leave empty for no password"
                    />
                  </div>

                  <button
                    onClick={generateShare}
                    className="btn bg-floppy-purple hover:bg-floppy-purple/80 text-white border-0 rounded-2xl w-full"
                  >
                    <Link className="w-5 h-5" />
                    Generate Share Link
                  </button>
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="alert alert-success">
                    <CheckCircle className="w-6 h-6" />
                    <div>
                      <div className="font-bold">Share link generated!</div>
                      <div className="text-sm">Your file is ready to share</div>
                    </div>
                  </div>

                  <div className="form-control">
                    <label className="label">
                      <span className="label-text font-medium">Share Link</span>
                    </label>
                    <div className="join w-full">
                      <input
                        type="text"
                        value={shareUrl}
                        readOnly
                        className="input input-bordered join-item flex-1 text-sm"
                      />
                      <button
                        onClick={copyToClipboard}
                        className="btn bg-floppy-blue hover:bg-floppy-blue/80 text-white border-0 join-item"
                      >
                        <Copy className="w-4 h-4" />
                      </button>
                    </div>
                  </div>

                  <div className="divider">OR</div>

                  {/* Email Section */}
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2 mb-4">
                      <Mail className="w-5 h-5 text-floppy-purple" />
                      <span className="font-medium text-lg">Email the download link</span>
                    </div>

                    {emailSent ? (
                      <div className="alert alert-success">
                        <CheckCircle className="w-5 h-5" />
                        <div>
                          <div className="font-bold">Email sent successfully!</div>
                          <div className="text-sm">The download link has been sent to {emailData.recipientEmail}</div>
                        </div>
                      </div>
                    ) : (
                      <>
                        <div className="form-control">
                          <label className="label">
                            <span className="label-text font-medium">Recipient Email</span>
                            <span className="label-text-alt text-red-500">Required</span>
                          </label>
                          <input
                            type="email"
                            value={emailData.recipientEmail}
                            onChange={(e) => setEmailData(prev => ({ ...prev, recipientEmail: e.target.value }))}
                            className="input input-bordered w-full"
                            placeholder="<EMAIL>"
                            disabled={isSendingEmail}
                          />
                        </div>

                        <div className="form-control">
                          <label className="label">
                            <span className="label-text font-medium">Your Name</span>
                            <span className="label-text-alt">Optional</span>
                          </label>
                          <input
                            type="text"
                            value={emailData.senderName}
                            onChange={(e) => setEmailData(prev => ({ ...prev, senderName: e.target.value }))}
                            className="input input-bordered w-full"
                            placeholder="Your name (optional)"
                            disabled={isSendingEmail}
                          />
                        </div>

                        <button
                          onClick={sendEmail}
                          disabled={!emailData.recipientEmail || isSendingEmail}
                          className="btn bg-floppy-green hover:bg-floppy-green/80 text-white border-0 rounded-2xl w-full disabled:opacity-50"
                        >
                          {isSendingEmail ? (
                            <>
                              <span className="loading loading-spinner loading-sm"></span>
                              Sending Email...
                            </>
                          ) : (
                            <>
                              <Mail className="w-5 h-5" />
                              Send Download Link
                            </>
                          )}
                        </button>
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>
            <div className="modal-backdrop" onClick={closeShareModal}></div>
          </div>
        )}
      </div>
    </div>
  );
}

export default App;
